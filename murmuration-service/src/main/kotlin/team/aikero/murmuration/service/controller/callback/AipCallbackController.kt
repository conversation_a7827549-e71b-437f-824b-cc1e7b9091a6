package team.aikero.murmuration.service.controller.callback

import com.fasterxml.jackson.databind.node.ObjectNode
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log

@RestController
class AipCallbackController {
    
    @PreCheckIgnore
    @PostMapping("/aip/callback")
    fun callback(@RequestBody body: ObjectNode) {
        log.info { "接收到 AIP 回调请求: $body" }
    }
}