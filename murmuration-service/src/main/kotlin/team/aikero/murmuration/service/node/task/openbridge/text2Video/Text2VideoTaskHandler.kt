package team.aikero.murmuration.service.node.task.openbridge.text2Video

import team.aikero.blade.oss.OssTemplate
import team.aikero.blade.oss.OssTemplateExt.transferFrom
import team.aikero.murmuration.common.req.task.Text2VideoRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.node.task.openbridge.OpenBridgeClient
import team.aikero.murmuration.service.utils.transferFrom
import java.net.URI

/**
 * 图生视频
 */
@TaskIdentifier(
    supplier = Supplier.MIDJOURNEY,
    ability = Ability.IMAGE_TO_VIDEO
)
class Text2VideoTaskHandler(
    private val openBridgeClient: OpenBridgeClient,
    private val ossTemplate: OssTemplate
) : TaskHandler<Text2VideoRequest, String> {
    override fun create(request: Text2VideoRequest): String {
        val taskId =
            openBridgeClient.text2video(ossTemplate.transferFrom(request.imageUrl),request.prompt)
        return taskId
    }

    override fun query(
        request: Text2VideoRequest,
        context: String
    ): TaskResult<List<TaskHandlerResult>> {
        val generateTask = openBridgeClient.getGenerateTask(context)
        return generateTask.map {
            val transferred = ossTemplate.transferFrom(it.map(URI::create))
            transferred.map(TaskHandlerResult::image)
        }
    }

}
