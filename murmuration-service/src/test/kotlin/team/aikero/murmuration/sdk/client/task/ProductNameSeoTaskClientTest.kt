package team.aikero.murmuration.sdk.client.task

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.andData
import team.aikero.murmuration.MurmurationApplication
import team.aikero.murmuration.common.req.task.ProductNameSeoRequest
import java.time.Duration
import java.util.UUID

@DisplayName("商品标题SEO生成 FeignClient 测试")
@SpringBootTest(
    classes = [MurmurationApplication::class],
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = [
        "server.port=8080",
        "domain.nest-api=http://localhost:8080",
    ]
)
class ProductNameSeoTaskClientTest(@Autowired val productNameSeoTaskClient: ProductNameSeoTaskClient) {
    
    @Test
    fun demo() = withSystemUser {
        // 业务数据的ID，比如 product_id
        val bizId = UUID.randomUUID().toString()
        
        // 业务数据的类型，比如 product
        val bizType = "UNIT_TEST"

        // 请求对象
        val request = ProductNameSeoRequest(
            productName = "BE女装|2025秋季欧美外贸复古可拆卸Lulu卫衣短款收腰拉链外套女",
            productCategory = "女装-上装类-卫衣帽衫",
            targetLanguage = "en",
        )

        // 超时时间
        val timeout = Duration.ofSeconds(10)

        // 同步调用SDK
        val response = productNameSeoTaskClient.executeTask(
            bizId = bizId,
            bizType = bizType,
            request = request,
            timeout = timeout,
        )

        val result = response.andData().first()
        
        // 翻译结果
        val text = result.getAttributesAs<String>()
    }
}